import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import Markdown from '@/modules/platform/components/markdown';
import { useUserConfigContext } from '@/modules/platform/contexts/config.context';
import { SubModules } from '@/modules/platform/interfaces/modules';
import React, { useEffect, useMemo, useState } from 'react';
import MdEditor from 'react-markdown-editor-lite';
import OptionsMenu from '../options-menu';
import RegenerationModal from '@/modules/platform/components/regeneration-modal';
import { useI2RPollingContext } from '../../contexts/polling.context';
import LoadingSpinner from '@/components/loading-spinner';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import { marked } from 'marked';
import { IRequirementDocument } from '@/modules/platform/interfaces/requirement-document';
import VersionNavigator from '@/components/version-navigator';
import useRequirementDocumentVersionSwitching from '@/modules/platform/utils/hooks/requirement-document-version-switching';
import { useMutation } from '@tanstack/react-query';
import axiosInstance from '@/utils/axios';
import { Controller, useForm } from 'react-hook-form';
import Textarea from '@/components/textarea';
import { PaperAirplaneIcon } from '@heroicons/react/24/outline';

interface IPrdProps {
  chatId: string;
  userPrompt: string;
  setSelectedTab: React.Dispatch<React.SetStateAction<string>>;
}

const Prd = ({ chatId, userPrompt, setSelectedTab }: IPrdProps) => {
  const { control } = useForm();
  const { jiraConfig } = useUserConfigContext();

  const {
    prdPollingEnabled,
    setPrdPollingEnabled,
    prdData,
    setPrdData,
    prdGenerationError,
    pollingEnabled,
    setPollingEnabled,
  } = useI2RPollingContext();

  const [isDocumentLoading, setIsDocumentLoading] = useState<boolean>(
    prdPollingEnabled || !prdData.description || !prdGenerationError,
  );
  const [isRegenerationModalOpen, setIsRegenerationModalOpen] = useState<boolean>(false);
  const [regenerationConfig, setRegenerationConfig] = useState({
    id: '',
    type: '',
  });
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [updatedDescription, setUpdatedDescription] = useState<string>('');

  const { handleVersionSwitching } = useRequirementDocumentVersionSwitching(
    prdData,
    setPrdData,
    setIsDocumentLoading,
  );

  const prdConstants = useTranslations('I2R.prd');
  const tabsConstants = useTranslations('I2R.homepage.tabs');
  const editConstants = useTranslations('I2R.edit');
  const homepageConstants = useTranslations('I2R.homepage');

  const handleExportAsWord = async () => {
    try {
      const markedDescription = marked(prdData?.description ?? '');
      const response = await axiosInstance.post(
        '/api/export/word',
        { title: prdData?.title, content: markedDescription },
        {
          responseType: 'blob',
          headers: { 'Content-Type': 'application/json' },
        },
      );

      if (response.status !== 200) {
        log.warn(`Failed to export Word document. Status: ${response.status}`);
        return;
      }

      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `${prdData?.title}.docx`;
      document.body.appendChild(link);
      link.click();
      link.remove();

      URL.revokeObjectURL(url);
    } catch (error) {
      log.warn('Error exporting Word document:', error);
    }
  };

  const handleEpicsAndStoriesGeneration = async () => {
    if (!jiraConfig) {
      log.warn('jiraConfigId is not defined');
      return;
    }

    try {
      const response = await axiosInstance.post('/api/i2r/generate', {
        config_id: jiraConfig.id,
        prompt: userPrompt,
        chat_id: chatId,
        document_id: prdData?.id,
      });

      if (response.data?.chat_id) {
        setPollingEnabled(true);
        setSelectedTab(tabsConstants('epics'));
      }
    } catch (error) {
      log.warn('Error in epics and stories generation', error);
    }
  };

  const openRegenerationModal = () => {
    setIsRegenerationModalOpen(true);
  };

  const closeRegenerationModal = () => {
    setIsRegenerationModalOpen(false);
  };

  const regeneratePrd = async (userInput: string) => {
    const response = await axiosInstance.post('/api/i2r/regenerate', {
      prd_id: regenerationConfig.id,
      feedback: userInput,
    });
    return response.data;
  };

  const mutation = useMutation({
    mutationFn: (userInput: string) => regeneratePrd(userInput),
    onSuccess: (data) => {
      if (data?.chat_id) {
        setIsDocumentLoading(true);
        setPrdData({} as IRequirementDocument);
        setPrdPollingEnabled(true);
        setIsRegenerationModalOpen(false);
      }
    },
    onError: (error) => {
      setIsRegenerationModalOpen(false);
      log.warn('Error in regeneration', error);
    },
  });

  const handleRegeneration = async (userInput: string) => {
    mutation.mutate(userInput);
  };

  const onEditSave = async () => {
    try {
      const response = await axiosInstance.put(
        '/api/platform/requirement-document',
        {
          title: prdData?.title,
          description: updatedDescription || prdData?.description,
        },
        {
          params: { id: prdData?.id },
        },
      );
      if (response.data?.id) {
        setPrdData(response.data);
        setIsEditing(false);
      }
    } catch (error) {
      log.warn(error);
      setIsEditing(false);
    }
  };

  const isRegenerationEnabled = useMemo(() => {
    return !prdData?.doesAnyVersionHaveTickets && !prdData?.regenerated && !pollingEnabled;
  }, [prdData, pollingEnabled]);

  const isEditEnabled = useMemo(() => {
    return !prdData?.doesAnyVersionHaveTickets && !pollingEnabled;
  }, [prdData, pollingEnabled]);

  useEffect(() => {
    setIsDocumentLoading(true);
    if (prdData.description) {
      setIsDocumentLoading(false);
    }
  }, [prdData]);

  if (prdGenerationError) {
    return <p className="label-s py-4 text-danger">{prdConstants('error')}</p>;
  } else if (isDocumentLoading) {
    return (
      <div className="flex w-full justify-center rounded-lg border bg-white p-1">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <p className="label-m text-secondary-neutral-900">{prdConstants('content')}</p>
        <Button
          variant={ButtonVariant.GHOST}
          className="border-secondary-neutral-200"
          onClick={handleExportAsWord}
        >
          {prdConstants('exportAsWordButton')}
        </Button>
      </div>
      <div className="rounded-lg border bg-white p-4">
        {isEditing ? (
          <div className="flex flex-col gap-4">
            <MdEditor
              defaultValue={prdData?.description}
              renderHTML={(text) => <Markdown content={text} />}
              onChange={(content) => setUpdatedDescription(content.text)}
              className="min-h-72 w-full"
              view={{ menu: false, md: true, html: false }}
            />
            <div className="flex justify-end gap-4">
              <Button variant={ButtonVariant.SOLID} onClick={onEditSave}>
                {editConstants('saveButton')}
              </Button>
              <Button variant={ButtonVariant.BORDERED} onClick={() => setIsEditing(false)}>
                {editConstants('cancelButton')}
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-secondary-neutral-800">
            <Markdown content={prdData?.description ?? ''} />
            <VersionNavigator
              currentVersion={prdData?.version ?? 1}
              totalVersions={prdData?.totalVersions ?? 1}
              handleVersionSwitching={handleVersionSwitching}
            />
          </div>
        )}
      </div>

      <div className="flex items-center justify-between">
        <OptionsMenu
          isLikeEnabled={prdData?.liked === null || !prdData?.liked}
          isDislikeEnabled={prdData?.liked === null || prdData?.liked}
          isEditEnabled={isEditEnabled}
          isRegenerateEnabled={isRegenerationEnabled}
          showPublish={false}
          openRegenerationModal={openRegenerationModal}
          setRegenerationConfig={setRegenerationConfig}
          onEdit={() => setIsEditing(true)}
          type={SubModules.PRD}
          id={prdData?.id ?? ''}
        />
        <div className="w-fit">
          <Button
            variant={ButtonVariant.SOLID}
            className="w-fit"
            onClick={handleEpicsAndStoriesGeneration}
            isDisabled={prdData?.doesAnyVersionHaveTickets || pollingEnabled}
          >
            {prdConstants('generateEpicsAndUserStoriesButton')}
          </Button>
        </div>
      </div>
      <RegenerationModal
        isOpen={isRegenerationModalOpen}
        closeModal={closeRegenerationModal}
        triggerRegeneration={handleRegeneration}
      />

      {/* New Idea Input Section */}
      <div className="rounded-lg border bg-white">
        <Controller
            name="idea"
            control={control}
            rules={{ required: homepageConstants('inputs.idea.error') }}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder={homepageConstants('inputs.idea.placeholder')}
                // isInvalid={!!errors.idea}
                // errorMessage={errors.idea?.message}
                endContent={
                  <PaperAirplaneIcon
                    className="h-6 w-6 cursor-pointer text-secondary-neutral-600"
                    // onClick={handleSubmit(handleIdeaSubmission)}
                  />
                }
                isRequired
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    // prevent a new line from being added
                    e.preventDefault();
                  }
                }}
                onKeyUp={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    // handleSubmit(handleIdeaSubmission)();
                  }
                }}
              />
            )}
          />
      </div>
    </div>
  );
};

export default Prd;
